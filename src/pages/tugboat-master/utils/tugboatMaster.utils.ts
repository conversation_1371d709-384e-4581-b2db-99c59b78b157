import axios from "axios";
import { FETCH_TUGBOATS } from "../tugboatMaster.apis";

export const getTugboats = async (page: number = 1, searchText: string = "") => {
  const response = await axios.post(`${import.meta.env.VITE_APP_API_URL}${FETCH_TUGBOATS}`, {
    params: {
      query: "{id,name,country_id{id,name,name_ar}}",
      filter: searchText
        ? `['|', ('id','ilike','${searchText}'),('name','ilike','${searchText}')]`
        : "[]",
      page: page,
    },
  });
  return response.data.result;
};
