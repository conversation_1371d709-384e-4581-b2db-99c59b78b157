import axios from "axios";
import { FETCH_VESSELS } from "../vesselMaster.apis";
import { Vessel } from "../vesselMaster.interfaces";

export const getVessels = async (
  page: number = 1,
  searchText: string = ""
): Promise<{
  result: Vessel[];
  total_pages: number;
  count: number;
}> => {
  const response = await axios.post(`${import.meta.env.VITE_APP_API_URL}${FETCH_VESSELS}`, {
    params: {
      query: "{id,name,country_id{id,name,name_ar}}",
      filter: searchText
        ? `['|', ('id','ilike','${searchText}'),('name','ilike','${searchText}')]`
        : "[]",
      page: page,
    },
  });
  return response.data.result;
};
