import { Button } from "@/components/ui/button";
import React, { useEffect } from "react";
import { useDebounce } from "@/hooks/useDebounce";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Plus, Pencil, Trash2, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useVesselMaster } from "./utils/useVesselMaster";
import { VesselUpdateModal } from "./components/vesselMasterUpdateModal";
import { Pagination } from "@/components/ui/pagination";

export const VesselMaster = () => {
  const {
    vessels,
    open,
    selectedVessel,
    selectedVessels,
    handleSelectAll,
    handleSelectVessel,
    handleOpen,
    handleEdit,
    handleClose,
    handleSubmit,
    handleDeleteSelected,
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    fetchVessels,
    searchText,
    setSearchText,
  } = useVesselMaster();

  const debouncedSearch = useDebounce(searchText);

  useEffect(() => {
    fetchVessels(1, debouncedSearch);
  }, [debouncedSearch]);

  const handlePageChange = (page: number) => {
    fetchVessels(page, debouncedSearch);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  const handleClearSearch = () => {
    setSearchText("");
  };
  return (
    <div>
      <div className="mx-3 flex justify-between mb-4">
        <Button
          variant="destructive"
          className="flex items-center gap-2"
          onClick={handleDeleteSelected}
          disabled={selectedVessels.length === 0}
          size={"sm"}
        >
          <Trash2 className="h-4 w-4" />
          Delete
        </Button>
        <div className="flex gap-2 items-center">
          <Button
            variant="default"
            onClick={handleOpen}
            className="flex items-center gap-2"
            size={"sm"}
          >
            <Plus className="h-4 w-4" />
            Create Vessel
          </Button>
          <div className="flex gap-2 items-center">
            <div className="relative w-64">
              <Input
                type="text"
                placeholder="Search by ID or name"
                value={searchText}
                onChange={handleSearch}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                  }
                }}
                className="w-full pr-8 h-8 py-2"
              />
              {searchText && (
                <button
                  onClick={handleClearSearch}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={vessels.length > 0 && selectedVessels.length === vessels.length}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Country</TableHead>
              <TableHead className="w-12">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {vessels.map((vessel) => (
              <TableRow key={vessel.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedVessels.includes(vessel.id)}
                    onCheckedChange={(checked) => handleSelectVessel(checked as boolean, vessel.id)}
                  />
                </TableCell>
                <TableCell>{vessel.name}</TableCell>
                <TableCell>{vessel.country_id?.name}</TableCell>
                <TableCell>
                  <Button variant="ghost" size="icon" onClick={() => handleEdit(vessel)}>
                    <Pencil className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
      />

      <VesselUpdateModal
        isOpen={open}
        onClose={handleClose}
        initialData={selectedVessel}
        onSubmit={handleSubmit}
      />
    </div>
  );
};
