import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { Vessel, FormData } from "../vesselMaster.interfaces";
import { useState, useEffect } from "react";
import axios from "axios";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface VesselUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Vessel | null;
  onSubmit: (data: FormData) => Promise<void>;
}

const validationSchema = Yup.object({
  name: Yup.string().required("Name is required"),
  country_id: Yup.number().required("Country is required"),
});

export const VesselUpdateModal = ({
  isOpen,
  onClose,
  initialData,
  onSubmit,
}: VesselUpdateModalProps) => {
  const initialValues: FormData = {
    name: initialData?.name || "",
    country_id: initialData?.country_id?.id || 0,
  };

  const [countries, setCountries] = useState<{ id: number, name: string }[]>([]);

  // Debug logging
  console.log("VesselUpdateModal - initialData:", initialData);
  console.log("VesselUpdateModal - initialValues:", initialValues);
  console.log("VesselUpdateModal - countries:", countries);

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.post(
          `${import.meta.env.VITE_APP_API_URL}/read/many/country.master`,
          {
            params: {
              query: "{id,name}",
              filter: "[]",
            },
          }
        );
        setCountries(response.data.result.result);
      } catch (error) {
        console.error("Error fetching countries:", error);
      }
    };

    fetchCountries();
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{initialData ? "Edit Vessel" : "Create Vessel"}</DialogTitle>
        </DialogHeader>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          enableReinitialize={true}
          onSubmit={async (values) => {
            await onSubmit(values);
          }}
        >
          {({ errors, touched, getFieldProps, setFieldValue, values }) => {
            // Debug logging for form values
            console.log("Form values:", values);
            console.log("Country ID value:", values.country_id);

            return (
              <Form className="space-y-4 mt-4 px-4 pb-4">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium">
                    Name
                  </label>
                  <Input
                    id="name"
                    {...getFieldProps("name")}
                    className={errors.name && touched.name ? "border-red-500" : ""}
                    placeholder="Enter vessel name"
                  />
                  {errors.name && touched.name && (
                    <div className="text-red-500 text-sm">{errors.name}</div>
                  )}
                </div>
                <div className="space-y-2">
                  <label htmlFor="country_id" className="text-sm font-medium">
                    Country
                  </label>
                  <Select
                    value={values.country_id ? values.country_id.toString() : ""}
                    onValueChange={(value) => setFieldValue("country_id", parseInt(value))}
                  >
                    <SelectTrigger className={errors.country_id && touched.country_id ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select a country" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem key={country.id} value={country.id.toString()}>
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.country_id && touched.country_id && (
                    <div className="text-red-500 text-sm">{errors.country_id}</div>
                  )}
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button type="button" variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button type="submit">{initialData ? "Update" : "Create"}</Button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};
