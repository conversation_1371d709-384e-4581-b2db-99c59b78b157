"use client";

import * as Popover from "@radix-ui/react-popover";
import { KeenIcon } from "@/components";
import { cn } from "@/lib/utils";

interface MultiSelectProps<T> {
  options: T[];
  selectedValues: T[];
  onChange: (selected: T[]) => void;
  labelKey: keyof T;
  valueKey: keyof T;
  placeholder?: string;
  isDisabled?: boolean;
  className?: string;
}

export function MultiSelect<T extends Record<string, any>>({
  options,
  selectedValues,
  onChange,
  labelKey,
  valueKey,
  placeholder = "Select...",
  isDisabled = false,
  className,
}: MultiSelectProps<T>) {
  const toggleSelection = (item: T) => {
    const exists = selectedValues.some((val) => val[valueKey] === item[valueKey]);
    if (exists) {
      onChange(selectedValues.filter((val) => val[valueKey] !== item[valueKey]));
    } else {
      onChange([...selectedValues, item]);
    }
  };

  const allSelected = options.length === selectedValues.length;

  const toggleAll = () => {
    if (allSelected) {
      onChange([]);
    } else {
      onChange([...options]);
    }
  };

  return (
    <Popover.Root>
      <Popover.Trigger asChild>
        <button
          disabled={isDisabled}
          className={cn(
            "px-4 py-2 border rounded bg-white text-sm w-full text-left flex items-center justify-between",
            "hover:border-primary/50 focus:border-primary/50 outline-none",
            className
          )}
          type="button"
        >
          <div className="flex flex-wrap gap-2 items-center flex-1 min-h-[24px]">
            {selectedValues.length > 0 ? (
              selectedValues.map((val) => (
                <span
                  key={val[valueKey]}
                  className="inline-flex items-center gap-1 px-2 py-1 text-xs rounded bg-purple-100 text-purple-800"
                >
                  {val[labelKey]}
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleSelection(val);
                    }}
                    className="hover:text-purple-950"
                  >
                    ×
                  </button>
                </span>
              ))
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>
          <KeenIcon icon="down" className="text-gray-500 ml-2 flex-shrink-0" />
        </button>
      </Popover.Trigger>
      <Popover.Portal>
        <Popover.Content
          className="p-4 bg-white border rounded shadow max-h-64 overflow-auto z-50 min-w-[200px]"
          sideOffset={5}
        >
          <div className="space-y-2">
            <label className="flex items-center gap-2 p-2 cursor-pointer hover:bg-gray-50 rounded">
              <input
                type="checkbox"
                checked={allSelected}
                onChange={toggleAll}
                className="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <span className="font-medium">Select All</span>
            </label>
            <div className="h-px bg-gray-200" />
            {options.map((option) => {
              const isChecked = selectedValues.some((val) => val[valueKey] === option[valueKey]);
              return (
                <label
                  key={option[valueKey]}
                  className="flex items-center gap-2 p-2 cursor-pointer hover:bg-gray-50 rounded"
                >
                  <input
                    type="checkbox"
                    checked={isChecked}
                    onChange={() => toggleSelection(option)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <span>{option[labelKey]}</span>
                </label>
              );
            })}
          </div>
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
}
