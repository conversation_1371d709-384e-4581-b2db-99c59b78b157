import { type TLanguageCode } from "@/i18n";

export interface AuthModel {
  access_token: string;
  // refreshToken?: string;
  // api_token?: string;
  image: string;
  login_email: string;
  name: string;
  user_id: number;
  role: string;
}

export interface UserModel {
  error: any;
  id: number;
  username: string;
  password: string | undefined;
  email: string;
  first_name: string;
  last_name: string;
  fullname?: string;
  occupation?: string;
  companyName?: string;
  phone?: string;
  roles?: number[];
  pic?: string;
  language?: TLanguageCode;
  auth?: AuthModel;
}
