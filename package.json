{"name": "metronic-tailwind-react", "private": true, "version": "9.1.2", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --fix", "preview": "vite preview"}, "dependencies": {"@auth0/auth0-spa-js": "^2.1.3", "@emotion/cache": "^11.13.1", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@faker-js/faker": "^9.1.0", "@firebase/app": "^0.10.15", "@firebase/auth": "^1.8.0", "@firebase/firestore": "^4.7.4", "@formatjs/intl-pluralrules": "^5.3.4", "@formatjs/intl-relativetimeformat": "^11.4.4", "@mui/base": "5.0.0-beta.40", "@mui/material": "^6.1.6", "@mui/utils": "^6.1.6", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.59.20", "@tanstack/react-table": "^8.20.5", "@types/highcharts": "^7.0.0", "apexcharts": "3.52.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.0.0", "formik": "^2.4.6", "highcharts": "^12.2.0", "highcharts-react-official": "^3.2.1", "leaflet": "^1.9.4", "lucide-react": "^0.456.0", "mini-svg-data-uri": "^1.4.4", "next-themes": "^0.4.3", "notistack": "^3.0.1", "postcss-preset-env": "^10.1.0", "qs": "^6.13.0", "react": "^18.3.1", "react-apexcharts": "1.4.1", "react-d3-tree": "^3.6.6", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-inlinesvg": "^4.1.4", "react-intl": "^6.8.7", "react-leaflet": "^4.2.1", "react-query": "^3.39.3", "react-router": "^6.28.0", "react-router-dom": "^6.28.0", "react-select": "^5.10.1", "react-to-print": "^3.0.5", "sonner": "^1.7.0", "styled-components": "^6.1.13", "stylis": "^4.3.4", "stylis-plugin-rtl": "^2.1.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vite-plugin-windicss": "^1.9.3", "yup": "^1.4.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.14.0", "@types/leaflet": "^1.9.14", "@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-helmet": "^6.1.11", "@types/styled-components": "^5.1.34", "@types/stylis": "^4.2.6", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^5.1.0-rc-fb9a90fa48-20240614", "eslint-plugin-react-refresh": "^0.4.14", "postcss": "^8.4.49", "prettier": "^3.3.3", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "typescript-eslint": "^8.14.0", "vite": "^5.4.11"}}